"use client";

import { useGetAccounts } from "@/components/banking/use-get-accounts";
import { notFound } from "next/navigation";
import AccountTransactions from "@/components/transactions/account-transactions";
import AccountDeposits from "@/components/time-deposits/account-deposits";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatDistanceToNow, subYears, format } from "date-fns";
import { BalanceTooltip } from "@/components/transactions/balance-tooltip";
import { useEffect, useState, useMemo, use } from "react";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { DateRange } from "react-day-picker";
import { CalendarIcon, RefreshCw, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Loading } from "@/components/ui/loader";
import { processTransactionData } from "@/app/(main)/statements/[id]/page";
import { BarChartCard } from "@/components/bar-chart-statements";
import { PieChartCard } from "@/components/pie-chart-statements";

const chartConfig = {
  income: {
    label: "Income",
    color: "hsl(var(--chart-2))",
  },
  expense: {
    label: "Expense",
    color: "hsl(var(--chart-1))",
  },
};

interface BankingDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

type DateRangeType = {
  from: Date;
  to?: Date;
};

type CustomDateRange = {
  label: string;
  from: Date;
  to?: Date;
};

type TimeRange = {
  label: string;
  duration: Date;
};

// Define time ranges with explicit types
type TimeRangeKey = "custom" | keyof typeof timeRanges;
const timeRanges = {
  custom: { label: "Custom Range", duration: new Date(new Date().getFullYear() - 4, 0, 1) },
  latest_5y: {
    label: "Latest 5 years",
    duration: new Date(new Date().getFullYear() - 4, 0, 1),
  },
  latest_4y: {
    label: "Latest 4 years",
    duration: new Date(new Date().getFullYear() - 3, 0, 1),
  },
  latest_3y: {
    label: "Latest 3 years",
    duration: new Date(new Date().getFullYear() - 2, 0, 1),
  },
  latest_2y: {
    label: "Latest 2 years",
    duration: new Date(new Date().getFullYear() - 1, 0, 1),
  },
  this_year: {
    label: "This year",
    duration: new Date(new Date().getFullYear(), 0, 1),
  },
} as const;

export default function BankingDetailsPage({
  params,
}: BankingDetailsPageProps) {
  const [timeRange, setTimeRange] = useState<TimeRangeKey>("latest_5y");
  const [activeTab, setActiveTab] = useState<string>("transactions");
  const [customDateRange, setCustomDateRange] = useState<DateRange | undefined>(
    undefined
  );

  const endOfDay = useMemo(() => {
    const date = new Date();
    date.setHours(23, 59, 59, 999);
    cons
    return date;
  }, []);

  // Use hook at the top level with corrected params
  const { data, isLoading, error, refetch } = useGetAccounts({
    accountId: use(params).id,
    startDate:
      timeRange === "custom" && customDateRange?.from
        ? customDateRange.from
        : (timeRanges[timeRange].duration ?? subYears(new Date(), 1)),
    endDate:
      timeRange === "custom" && customDateRange?.to
        ? customDateRange.to
        : endOfDay,
    shouldFetch: true,
  });
    const chartData = processTransactionData(data?.transactions ?? []);

  const handleCustomDateChange = (range: DateRange | undefined) => {
    if (!range) {
      setCustomDateRange(undefined);
      setTimeRange("latest_5y"); // Reset to default range when clearing
      return;
    }

    if (range.from) {
      setCustomDateRange(range);
      setTimeRange("custom");
    }
  };

  // Remove the getCurrentRange call from the hook dependencies
  const getCurrentRange = (): CustomDateRange | TimeRange => {
    if (timeRange === "custom" && customDateRange?.from) {
      return {
        label: customDateRange.to
          ? `${format(customDateRange.from, "MMM d, yyyy")} - ${format(customDateRange.to, "MMM d, yyyy")}`
          : format(customDateRange.from, "MMM d, yyyy"),
        from: customDateRange.from,
        to: customDateRange.to,
      };
    }

    return timeRanges[timeRange].duration
      ? {
          label: timeRanges[timeRange].label,
          duration: timeRanges[timeRange].duration!,
        }
      : { label: "Last year", duration: subYears(new Date(), 1) };
  };

  const refreshAccountData = async () => {
    try {
      await refetch();
      toast.success("All account data refreshed successfully");
    } catch (error) {
      console.error("Error refreshing account data:", error);
      toast.error("Failed to refresh account data");
    }
  };

  useEffect(() => {
    if (
      data?.timeDeposits?.length &&
      (!data.transactions || data.transactions.length === 0)
    ) {
      setActiveTab("timeDeposits");
    }
  }, [data]);

  if (isLoading) {
    return <div className="flex justify-center items-center bg-opacity"><Loading /></div>;
  }

  if (error) {
    toast.error("Error loading account details");
  }

  if (!data) {
    notFound();
  }

  const { account, transactions, timeDeposits } = data;
  const hasTimeDeposits = (timeDeposits ?? []).length > 0;
  const hasTransactions = transactions?.length > 0;

  // Add handler for time range change
  const handleTimeRangeChange = (value: string) => {
    setTimeRange(value as TimeRangeKey);
    if (value !== "custom") {
      setCustomDateRange(undefined);
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-2 p-2 pt-0">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold tracking-tight">{account.name}</h2>
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={refreshAccountData}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh Data
          </Button>
          {timeRange === "custom" && (
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "justify-start text-left font-normal",
                    !customDateRange && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {customDateRange?.from ? (
                    customDateRange.to ? (
                      <>
                        {format(customDateRange.from, "LLL dd, y")} -{" "}
                        {format(customDateRange.to, "LLL dd, y")}
                      </>
                    ) : (
                      format(customDateRange.from, "LLL dd, y")
                    )
                  ) : (
                    <span>Pick a date range</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={customDateRange?.from}
                  selected={customDateRange}
                  onSelect={handleCustomDateChange}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          )}
          <Select value={timeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(timeRanges).map(([key, value]) => (
                <SelectItem key={key} value={key}>
                  {value.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid gap-2 md:grid-cols-3 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Balance</CardTitle>
            <BalanceTooltip
              balance={account.balance}
              availableBalance={account.balance}
              pendingBalance={0}
              lastUpdated={account.lastUpdated}
            />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {account.balance.toLocaleString("zh-TW", {
                style: "currency",
                currency: "TWD",
              })}
            </div>
            <p className="text-xs text-muted-foreground">
              Last updated{" "}
              {formatDistanceToNow(account.lastUpdated, { addSuffix: true })}
            </p>
          </CardContent>
        </Card>
        <BarChartCard
          title="Statements"
          description="Income vs Expense"
          data={chartData}
          xAxisKey="month"
          chartConfig={chartConfig}
        />
        <PieChartCard
          title="Statements"
          description="Income vs Expense"
          data={chartData}
          xAxisKey="month"
          chartConfig={chartConfig}
        />
      </div>

      {hasTimeDeposits && hasTransactions ? (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-2">
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="timeDeposits">Time Deposits</TabsTrigger>
          </TabsList>
          <TabsContent value="transactions">
            <AccountTransactions
              dataRange={{
                from:
                  timeRange === "custom" && customDateRange?.from
                    ? customDateRange.from
                    : timeRanges[timeRange].duration!,
                to:
                  timeRange === "custom" && customDateRange?.to
                    ? customDateRange.to
                    : new Date(),
              }}
              transactions={transactions}
              onTransactionUpdate={refetch}
            />
          </TabsContent>
          <TabsContent value="timeDeposits">
            <AccountDeposits
              timeDeposits={timeDeposits ?? []}
              onTimeDepositUpdate={() => {
                refetch();
              }}
            />
          </TabsContent>
        </Tabs>
      ) : hasTimeDeposits ? (
        <AccountDeposits
          timeDeposits={timeDeposits ?? []}
          onTimeDepositUpdate={() => {
            refetch();
          }}
        />
      ) : (
        <AccountTransactions
          dataRange={(() => {
            if (timeRange === "custom" && customDateRange?.from) {
              // If 'to' is missing, use 'from' as both start and end
              const from = customDateRange.from;
              const to = customDateRange.to ?? customDateRange.from;
              return { from, to };
            }
            // For predefined ranges, use duration as 'from' and today as 'to'
            const currentRange = getCurrentRange();
            if ("duration" in currentRange) {
              return { from: currentRange.duration, to: new Date() };
            }
            // Fallback: use today for both
            return { from: new Date(), to: new Date() };
          })()}
          transactions={transactions}
          onTransactionUpdate={() => {
            refetch();
          }}
        />
      )}
    </div>
  );
}
