import axios from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import { webhook, messagingApi, HTTPFetchError, validateSignature } from '@line/bot-sdk';
import { createGoogleGenerativeAI } from '@ai-sdk/google';
import { groq } from '@ai-sdk/groq';
import { 
  experimental_createMCPClient as createMCPClient, 
  generateText, generateObject
} from 'ai';
import { zepClient, zepMemory } from '@/lib/zepClient';
import { productsSchema } from '../schemas';
import { uploadImageToExpenseReportDrive } from '@/lib/google/upload-image-to-expense-report-drive';
import { appendExpenseReportToSheet } from '@/lib/google/append-expense-report-to-sheet';
import { 
  getDateRanges, 
  streamToBuffer,
  analyzeExpenseReportImage,
  createProductFlexMessage,
  getMessageContentWithRetry,
} from '../functions';
import { fetchBulletinFromSheets } from '@/lib/google/fetch-bulletin-from-sheets';
import { fetchProductsFromSheets } from '@/lib/google/fetch-products-from-sheets';
import { createAnnounceFlexMessage } from './flex-templates/bulletin';
import { determineChannel, sendToDiscordMessage } from '@/utils/discord-channel-router';

export const maxDuration = 300;

let systemPrompt = `
1.Do not convert user datetime, just add user timezone (+08:00) for tool input, such as 2025-05-16T20:00:00+08:00.
2.When user specify date without time, it means start_time: 23:59, end_time: 00:00.
3.Today is ${getDateRanges().today}
4.This week: ${getDateRanges().thisWeek.start} to ${getDateRanges().thisWeek.end}
5.This month: ${getDateRanges().thisMonth.start} to ${getDateRanges().thisMonth.end}
6.使用繁體中文回答
`

let userProfile: messagingApi.UserProfileResponse | null = null;
const sessionId = '96204366-571d-4528-9c02-e7206124dd3d'

const client = new messagingApi.MessagingApiClient({
  channelAccessToken: process.env.CHANNEL_ACCESS_TOKEN!,
});
const blobClient = new messagingApi.MessagingApiBlobClient({ 
  channelAccessToken: process.env.CHANNEL_ACCESS_TOKEN!,
});

const middlewareConfig = {
  channelSecret: process.env.CHANNEL_SECRET!,
};

const google = createGoogleGenerativeAI({
  apiKey: process.env.GOOGLE_GENERATIVE_AI_API_KEY
});

// Utility function to start loading status
async function startLoadingStatus(chatId: string, loadingSeconds: number = 5) {
  try {
    const response = await fetch('https://api.line.me/v2/bot/chat/loading/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.CHANNEL_ACCESS_TOKEN}`,
      },
      body: JSON.stringify({
        chatId,
        loadingSeconds,
      }),
    });
    
    if (!response.ok) {
      console.error('Failed to start loading status:', await response.text());
    }
  } catch (error) {
    console.error('Error starting loading status:', error);
  }
}

// Utility function for MCP tools retry
async function getMCPToolsWithRetry(
  mcpClient: any,
  retries = 0,
  maxRetries = 3,
  delayMs = 1000
) {
  try {
    // Get Zapier tools
    const zapierTools = await mcpClient.tools();
    
    // Get Smithery tools with error handling
    /*let smitheryTools = {};
    try {
      const smitheryClient = await createSmitheryClient();
      smitheryTools = Object.fromEntries(
        Object.entries(smitheryClient || {}).map(([key, value]) => [
          `smithery_${key}`, // Add prefix to avoid name conflicts
          value
        ])
      );
    } catch (error) {
      console.error('Error setting up Smithery client:', error);
    }*/

    // Combine both toolsets
    return {
      ...zapierTools,
      //...smitheryTools
    };
  } catch (error) {
    if (retries < maxRetries) {
      console.log(`Retry ${retries + 1}/${maxRetries} fetching MCP tools...`);
      // Wait with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delayMs * Math.pow(2, retries)));
      return getMCPToolsWithRetry(mcpClient, retries + 1, maxRetries, delayMs);
    }
    throw error;
  }
}

// Utility function to get user profile
async function getUserProfile(userId: string) {
  try {
    const profile = await client.getProfile(userId);
    return profile;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
}

const eventHandler = async (event: webhook.Event) => {
  console.log("===========================event================================", JSON.stringify(event, null, 2))
  if (event.type !== 'message' || !event.replyToken) return;

  // Type guard for group messages
  if ((event?.source?.type === "group" && 
    event.message.type === 'text' && 
    !event.message.text?.includes("@AI巧管家")) || 
    event?.source?.type === "group" && 
    event.message.type === 'image'
  ) {
    return;
  }

  // User ID check
  const allowedUserIds = ["U7d91a648c3b6a05921fc48017ff09581", "U01c0b00ebe5ca71d16613571a14fa4d4"];
  if (!allowedUserIds.includes(event?.source?.userId ?? "")) {
    return;
  }

  let mcpClient

  if (event.message.type === 'image') {
    try {
      // Get the image content using the messaging API
      const imageStream = await getMessageContentWithRetry(blobClient, event.message.id);
      const buffer = await streamToBuffer(imageStream);
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `expense_report_${timestamp}.jpg`;
      const base64Image = buffer.toString('base64');

      // Run multiple operations in parallel
      const [driveUploadResult, analyzedExpenseReport] = await Promise.all([
        uploadImageToExpenseReportDrive(buffer, filename),
        analyzeExpenseReportImage(base64Image)
      ]);
      const expenseReportData = {
        ...analyzedExpenseReport,
        googleDriveLink: driveUploadResult.webViewLink || undefined
      };
      const appendResult = await appendExpenseReportToSheet(expenseReportData);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [
          {
            type: 'text',
            text: `收支明細表已上傳！\n預覽試算表：${appendResult.spreadsheetUrl}\nDrive連結：${appendResult.googleDriveLink}`,
          },
        ],
      });

      return;
    } catch (error) {
      console.error('Error processing image:', error);
      await client.replyMessage({
        replyToken: event.replyToken,
        messages: [{ type: 'text', text: '圖片處理失敗，請稍後再試。' }],
      });
      return;
    }
  }  // Handle text messages
  if (event.message.type === 'text') {
    // Start loading status if we have a user ID
    if (event.source?.userId) {
      // Start loading status, but don't wait for it to finish before continuing
      startLoadingStatus(event.source.userId, 20).catch(console.error);
      
      // Get user profile when needed
      userProfile = await getUserProfile(event.source.userId);
      //console.log('User Profile:', userProfile);
    }

    if (event.message.text.includes("公告")){
      try {
        // Fetch bulletin data
        const bulletins = await fetchBulletinFromSheets();
        
        // Filter enabled bulletins and sort by date
        const enabledBulletins = bulletins
          .filter(bulletin => bulletin.enable)
          .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
        
        if (enabledBulletins.length === 0) {
          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [{ type: 'text', text: '目前沒有公告。' }],
          });
          return;
        }

        await client.replyMessage({
          replyToken: event.replyToken,
          messages: [{
            type: 'flex',
            altText: '公告',
            contents: createAnnounceFlexMessage(enabledBulletins)
          }],
        });
      } catch (error) {
        console.error('Error fetching bulletin:', error);
        await client.replyMessage({
          replyToken: event.replyToken,
          messages: [{ type: 'text', text: '抱歉，取得公告時發生錯誤。請稍後再試。' }],
        });
      }
      return;
    }

    if (event.message.text.includes("本月重點")){
      try {
        // Fetch products data
        const onSaleItems = await fetchProductsFromSheets();
        
        if (onSaleItems.length > 0) {
          let selectedItems = onSaleItems
          if (onSaleItems.length > 15) {
            // Shuffle the array using Fisher-Yates algorithm
            const shuffled = [...onSaleItems];
            for (let i = shuffled.length - 1; i > 0; i--) {
              const j = Math.floor(Math.random() * (i + 1));
              [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            
            // Take the first 15 items
            selectedItems = shuffled.slice(0, 12);
          }

          // Create the flexMessage structure with data array
          const flexMessage = createProductFlexMessage(selectedItems);
          
          console.log("flexMessage:", JSON.stringify(flexMessage, null, 2));
          
          // Send the flexMessage
          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: 'flex',
                altText: '特價商品',
                contents: {
                  type: "carousel",
                  contents: flexMessage[0].data.map(item => item as messagingApi.FlexBubble)
                }
              },
            ],
          });
        } else {
          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: 'text',
                text: '目前沒有特價商品',
              },
            ],
          });
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        await client.replyMessage({
          replyToken: event.replyToken,
          messages: [{ type: 'text', text: '抱歉，取得資料時發生錯誤。請稍後再試。' }],
        });
      }
      return;
    }

    mcpClient = await createMCPClient({
      name: 'zapier',
      transport: {
        type: 'sse',
        url: 'https://mcp.zapier.com/api/mcp/s/NDAyODViYjItZmY1ZC00NDk3LWJkMjgtZGJmZDYwNTU3YjgyOjA0OTVjZTU4LTRiNTEtNDNjYy1iYjgzLTI2M2RkYjhiOWNmMQ==/sse',
      },
    });
    
    const userPrompt = event.message.text.includes("本月主打") ? `使用google sheets工具查詢前3筆資料` : event.message.text;
    console.log("userPrompt", userPrompt);
    systemPrompt = event.message.text.includes("本月主打") ? `` : systemPrompt;
    const model = event.message.text.includes("尋找") || 
      event.message.text.includes("Search") ||
      event.message.text.includes("search") ||
      event.message.text.includes("查尋") ||
      event.message.text.includes("搜尋") ? 
      google('gemini-2.5-flash-preview-04-17', {
        useSearchGrounding: true,
        dynamicRetrievalConfig: {
          mode: 'MODE_DYNAMIC',
          dynamicThreshold: 0.8,
        },
      }) : groq('qwen-qwq-32b');

    const tools = await getMCPToolsWithRetry(mcpClient);

    const memory = await zepMemory(
      "2007415562", 
      sessionId, 
      "zep_f5d049fc3458737444e88488ab596495db64b4a00ae7f30bd750775184368fdc",
      userPrompt,
    );
    //console.log("memory: ", JSON.stringify(memory, null, 2))

    // Combine memory.prevMemoryMessages with the current user prompt for message history
    const historyMessages = (memory?.prevMemoryMessages || []).map(msg => ({
      role: (msg.role ?? 'user') as 'user' | 'assistant',
      content: msg.content,
    }));

    historyMessages.push({
      role: 'user',
      content: userPrompt,
    });

    //console.log("historyMessages: ",historyMessages)
    await sendToDiscordMessage(userPrompt, userProfile?.displayName);
    
    const result = await generateText({
      model,
      tools,
      maxSteps: 10,
      system: systemPrompt,
      messages: historyMessages,
    });
    console.log("result==============================>: ", JSON.stringify(result, null, 2))
    if (result.text && userPrompt === "使用google sheets工具查詢前3筆資料") {
      //console.log("toolResults: ", JSON.stringify(result.steps[0].toolResults[0].result.content, null, 2))
      const { object } = await generateObject({
        model: google('gemini-2.0-flash-exp'),
        schema: productsSchema,
        prompt: `Structured output this data:\n\n${JSON.stringify(result.steps[0].toolResults[0].result.content, null, 2)}`,
      });
      console.log("=====================object=========================", JSON.stringify(object, null, 2));

      if (object) {
        const onSaleItems = object.filter(item => item.onSale === true);
        if (onSaleItems.length > 0) {
          let selectedItems = onSaleItems
          if (onSaleItems.length > 15) {
            // Shuffle the array using Fisher-Yates algorithm
            const shuffled = [...onSaleItems];
            for (let i = shuffled.length - 1; i > 0; i--) {
              const j = Math.floor(Math.random() * (i + 1));
              [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
            }
            
            // Take the first 15 items
            selectedItems = shuffled.slice(0, 12);
          }

          // Create the flexMessage structure with data array
          const flexMessage = createProductFlexMessage(selectedItems);
          
          console.log("flexMessage:", JSON.stringify(flexMessage, null, 2));
          
          // Send the flexMessage
          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: 'flex',
                altText: '特價商品',
                contents: {
                  type: "carousel",
                  contents: flexMessage[0].data.map(item => item as messagingApi.FlexBubble)
                }
              },
            ],
          });
        } else {
          // No items on sale, send a different message
          await client.replyMessage({
            replyToken: event.replyToken,
            messages: [
              {
                type: 'text',
                text: '目前沒有特價商品',
              },
            ],
          });
        }
      }
    } else {
      const messages: messagingApi.Message[] = [
        {
          type: 'text',
          text: result.text,
        }
      ];

      // Add sources if they exist
      if (result?.sources && result.sources.length > 0) {
        const sourcesList = result.sources
          .map((source, index) => `${index + 1}. ${source.title}\n${source.url}`)
          .join('\n\n');

        messages.push({
          type: 'text',
          text: `參考來源：\n\n${sourcesList}`,
        });
      }

      await client.replyMessage({
        replyToken: event.replyToken,
        messages,
      });

      await zepClient.memory.add(sessionId, {
        messages: [
          { 
        role: "user", //userProfile?.displayName
        roleType: "user", 
        content: userPrompt 
          },
          { 
        role: "assistant", 
        roleType: "assistant", 
        content: result.text 
          }
        ]
      });
    }
  }
};

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = req.headers.get('x-line-signature') || '';

  const isValid = validateSignature(body, middlewareConfig.channelSecret, signature);

  if (!isValid) {
    return new Response('Invalid signature', { status: 401 });
  }

  const events: webhook.Event[] = JSON.parse(body).events;
  console.log("//////////////////events: ////////////////////", events)

  try {
    //await Promise.all(events.map((event) => eventHandler(event)));
    await eventHandler(events[0]);
    return NextResponse.json({ status: 'success' });
  } catch (err: unknown) {
    if (err instanceof HTTPFetchError) {
      console.error(err.status);
      console.error(err.headers.get('x-line-request-id'));
      console.error(err.body);
    } else if (err instanceof Error) {
      console.error(err);
    }

    return NextResponse.json({ status: 'error' }, { status: 500 });
  }
}