{"name": "badget", "version": "0.1.0", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:reset": "prisma db push --force-reset", "db:deploy": "prisma generate && prisma db push", "postinstall": "prisma generate"}, "packageManager": "bun@1.2.2", "dependencies": {"@ai-sdk/google": "^1.2.14", "@ai-sdk/groq": "^1.2.8", "@ai-sdk/openai": "^1.3.21", "@assistant-ui/react": "^0.7.80", "@assistant-ui/react-markdown": "^0.7.20", "@assistant-ui/react-syntax-highlighter": "^0.7.10", "@badget/shared": "*", "@blocknote/core": "^0.23.6", "@blocknote/mantine": "^0.23.6", "@blocknote/react": "^0.23.6", "@blocknote/shadcn": "^0.23.6", "@clerk/backend": "^1.24.1", "@clerk/nextjs": "^6.11.3", "@clerk/types": "^4.46.0", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-php": "^6.0.1", "@codemirror/lang-python": "^6.1.7", "@codemirror/lang-rust": "^6.0.1", "@codemirror/lang-sql": "^6.8.0", "@codemirror/lang-xml": "^6.1.0", "@copilotkit/react-core": "^1.4.7", "@copilotkit/react-textarea": "^1.4.7", "@copilotkit/react-ui": "^1.4.7", "@copilotkit/runtime": "^1.4.7", "@copilotkit/sdk-js": "^1.4.7", "@copilotkit/shared": "^1.4.7", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@fontsource/noto-sans-tc": "^5.2.5", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@getzep/zep-cloud": "^2.12.3", "@google/generative-ai": "^0.24.0", "@googleapis/calendar": "^9.7.9", "@hello-pangea/dnd": "^18.0.1", "@hono/clerk-auth": "^2.0.0", "@hono/zod-validator": "^0.4.2", "@hookform/resolvers": "^3.9.1", "@inngest/agent-kit": "^0.7.2", "@langchain/anthropic": "^0.3.13", "@langchain/community": "^0.3.30", "@langchain/core": "^0.3.40", "@langchain/exa": "^0.1.0", "@langchain/google-genai": "^0.1.8", "@langchain/groq": "^0.1.3", "@langchain/langgraph": "^0.2.46", "@langchain/ollama": "^0.2.0", "@langchain/openai": "^0.4.4", "@line/bot-sdk": "^9.9.0", "@lobehub/tts": "^1.28.3", "@mendable/firecrawl-js": "^1.17.0", "@modelcontextprotocol/sdk": "^1.13.1", "@napi-rs/canvas": "^0.1.70", "@neondatabase/serverless": "^0.10.4", "@next/codemod": "15.1.0", "@nextjournal/lang-clojure": "^1.0.0", "@prisma/client": "^6.5.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.4", "@radix-ui/react-visually-hidden": "^1.1.1", "@react-email/components": "^0.0.32", "@replit/codemirror-lang-csharp": "^6.2.0", "@schematichq/schematic-components": "^0.7.10", "@schematichq/schematic-react": "^1.2.4", "@schematichq/schematic-typescript-node": "^1.1.10", "@smithery/sdk": "^1.3.4", "@stripe/react-stripe-js": "^3.6.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.48.1", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.62.8", "@tanstack/react-query-devtools": "^5.62.8", "@tanstack/react-table": "^8.20.6", "@types/cookie": "^1.0.0", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.15", "@types/luxon": "^3.4.2", "@types/pdf-parse": "^1.1.4", "@types/react-google-picker": "^0.1.4", "@types/react-syntax-highlighter": "^15.5.13", "@types/uuid": "^10.0.0", "@uiw/react-codemirror": "^4.23.8", "@uiw/react-md-editor": "^4.0.5", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.3", "@upstash/workflow": "^0.2.4", "@vercel/kv": "2.0.0", "ai": "^4.3.13", "axios": "^1.7.9", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clerk": "^0.8.3", "clsx": "^2.1.1", "cmdk": "1.0.0", "cohere-ai": "^7.15.0", "convex": "^1.23.0", "cookie": "^1.0.2", "crypto-js": "^4.2.0", "csv-parse": "^5.6.0", "cuid": "^3.0.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.4.7", "embla-carousel-react": "^8.1.7", "exa-js": "^1.4.10", "framer-motion": "^12.4.2", "gapi-script": "^1.2.0", "google-auth-library": "^9.15.0", "googleapis": "^144.0.0", "groq-sdk": "^0.15.0", "hono": "4.6.3", "inngest": "^3.34.4", "langchain": "^0.3.15", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "luxon": "^3.5.0", "mime": "^4.0.6", "next": "^15.3.3", "next-themes": "^0.3.0", "nuqs": "^2.3.0", "ollama-ai-provider": "^1.2.0", "pdf-parse": "^1.1.1", "pptxgenjs": "^3.12.0", "prisma": "^6.9.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-big-calendar": "^1.17.1", "react-colorful": "^5.6.1", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-google-drive-picker": "^1.2.2", "react-google-picker": "^0.1.0", "react-hook-form": "^7.53.2", "react-icons": "^5.2.1", "react-json-view": "^1.21.3", "react-markdown": "^9.0.3", "react-resizable-panels": "^2.1.7", "react-spreadsheet": "^0.9.5", "react-syntax-highlighter": "^15.6.1", "react-textarea-autosize": "^8.5.6", "react-use": "^17.6.0", "recharts": "^2.13.3", "rehype-katex": "^7.0.1", "rehype-pretty-code": "^0.13.2", "rehype-stringify": "^10.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "^11.1.0", "resend": "^4.0.1", "server-only": "^0.0.1", "sonner": "^1.7.0", "tailwind-merge": "^3.0.1", "tailwind-scrollbar-hide": "^2.0.0", "tailwindcss-animate": "^1.0.7", "unified": "^11.0.5", "usehooks-ts": "^3.1.1", "uuid": "^11.0.3", "vaul": "^0.9.1", "xlsx": "^0.18.5", "zod": "^3.23.8", "zustand": "^5.0.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.13", "@types/node": "^20", "@types/react": "^18", "@types/react-big-calendar": "^1.16.1", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^15.1.0", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}