"use server";

import { prisma } from "@/lib/db";
import { auth } from "@clerk/nextjs/server";
import { AccountType, TimeDepositType, Prisma } from "@prisma/client";
import {
  isSameMonth,
  isSameYear,
  subMonths,
  endOfMonth,
  startOfMonth,
} from "date-fns";

interface AccountDetails {
  account: {
    id: string;
    name: string;
    type: AccountType;
    balance: number;
    accountNumber?: string;
    lastUpdated: Date;
  };
  transactions: {
    id: string;
    accountId: string;
    categoryId: string;
    date: Date;
    description: string;
    amount: number;
    type: string;
    suggestedCategoryId?: string | null;
    categoryValidated: boolean;
    category?: {
      id: string;
      name: string;
      icon: string;
    };
  }[];
  timeDeposits?: {
    id: string;
    accountId: string;
    categoryId: string;
    amount: number;
    interestRate: number;
    date: Date;
    certificateNo: string;
    period: string;
    type: TimeDepositType;
    description: string;
    currency: string;
  }[];
}

export async function getAccountDetails(
  accountId: string,
  startDate: Date,
  endDate?: Date
): Promise<{ success: boolean; data?: AccountDetails; error?: string }> {
  try {
    const { userId } = await auth();
    if (!userId) {
      throw new Error("Unauthorized");
    }

    console.log("startDate", startDate);
    console.log("endDate", endDate);
    // Get account details
    const balanceWhereClause = await balanceCause(endDate!);
    const account = await prisma.bankAccount.findFirst({
      where: {
        id: accountId,
        userId,
      },
      select: {
        id: true,
        name: true,
        accountType: true,
        originalId: true,
        updatedAt: true,
        Balance: {
          where: balanceWhereClause,
          orderBy: { date: "desc" },
          take: 1,
          select: {
            amount: true,
            date: true,
          },
        },
        Transaction: {
          where: {
            date: {
              gte: startDate,
              ...(endDate && { lte: endDate }),
            },
          },
          select: {
            id: true,
            date: true,
            type: true,
            amount: true,
            accountId: true,
            categoryId: true,
            categoryValidated: true,
            description: true,
            category: {
              select: {
                id: true,
                name: true,
                icon: true,
              },
            },
          },
          orderBy: {
            date: "desc",
          },
        },
        TimeDeposit: {
          where: {
            date: {
              gte: startDate,
              ...(endDate && { lte: endDate }),
            },
          },
          select: {
            id: true,
            accountId: true,
            categoryId: true,
            amount: true,
            interestRate: true,
            date: true,
            certificateNo: true,
            period: true,
            type: true,
            description: true,
            currencyIso: true,
          },
        },
      },
    });

    if (!account) {
      throw new Error("Account not found");
    }

    // Get transactions
    /*const transactions = await prisma.transaction.findMany({
      where: {
        accountId,
        //date: {
        //  gte: startDate,
        //},
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            icon: true,
          },
        },
      },
      orderBy: {
        date: "desc",
      },
    });*/

    return {
      success: true,
      data: {
        account: {
          id: account.id,
          name: account.name,
          type: account.accountType,
          balance: account.Balance[0]?.amount || 0,
          accountNumber: account.originalId || undefined,
          lastUpdated: account.Balance[0]?.date || account.updatedAt,
        },
        transactions: account.Transaction
          ? account.Transaction.map((tx) => ({
              id: tx.id!,
              accountId: tx.accountId!,
              categoryId: tx.categoryId!,
              date: tx.date,
              description: tx.description!,
              amount: Number(tx.amount),
              type: tx.type,
              categoryValidated: tx.categoryValidated,
              category: tx.category
                ? {
                    id: tx.category.id,
                    name: tx.category.name,
                    icon: tx.category.icon,
                  }
                : undefined,
            }))
          : [],
        timeDeposits: account.TimeDeposit
          ? account.TimeDeposit.map((td) => ({
              id: td.id,
              accountId: td.accountId!,
              categoryId: td.categoryId!,
              amount: Number(td.amount),
              interestRate: Number(td.interestRate),
              date: td.date,
              certificateNo: td.certificateNo!,
              period: td.period!,
              type: td.type,
              description: td.description!,
              currency: td.currencyIso,
            }))
          : [],
      },
    };
  } catch (error) {
    console.error("Error in getAccountDetails:", error);
    return {
      success: false,
      error: "Failed to fetch account details",
    };
  }
}

export async function balanceCause(endDate: Date) {
  // Get current date
  const now = new Date();

  // Check if endDate is current month/year
  const isCurrentMonth = isSameMonth(now, endDate) && isSameYear(now, endDate);

  let balanceWhereClause;

  if (isCurrentMonth) {
    // Get previous month's last day
    const prevMonthEnd = endOfMonth(subMonths(now, 1));
    balanceWhereClause = {
      date: {
        lte: prevMonthEnd,
      },
    };
  } else {
    // Get the specified month's range
    const monthStart = startOfMonth(endDate);
    const monthEnd = endOfMonth(endDate);
    balanceWhereClause = {
      date: {
        gte: monthStart,
        lte: monthEnd,
      },
    };
  }

  return balanceWhereClause;
}