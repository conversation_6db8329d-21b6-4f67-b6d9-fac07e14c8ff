import { z } from 'zod';

// 管理費自動轉帳
export const autoPayResidentSchema = z.object({
  printDate: z.string().describe("列印日期 in format YYYY年MM月DD日"),
  organizationId: z.string().describe("委託單位統編 - organization tax ID number"),
  records: z.array(z.object({
    residentId: z.string().describe("用戶代號 - user account number in format XX-XX-X"),
    amount: z.number().describe("金額 - payment amount as number"),
  })).describe("Array of payment records from the statement")
});

// 社區公告
export const announcementsSchema = z.array(
  z.object({
    id: z.number(),
    enable: z.boolean(),
    title: z.string().min(2),
    topic: z.string().min(2),
    content: z.string().min(8),
    date: z.string(),
    severity: z.string()
  })
)

// 商品
export const productsSchema = z.array(
  z.object({
    onSale: z.boolean(),
    id: z.number(),
    title: z.string(),
    slug: z.string(),
    price: z.number(),
    description: z.string(),
    category_id: z.number(),
    category_name: z.string(),
    "category.slug": z.string(),
    "category.image": z.string(),
    "images.0": z.string().describe("url address"),
    "images.1": z.string().optional().describe("url address"),
    "images.2": z.string().optional().describe("url address"),
    inStock: z.number(),
    rating: z.number(),
    ratingEmoji: z.string().describe("★"),
    ratingEmoji2: z.string().optional(),
    "Number of Reviews": z.number(),
    specs: z.string()
  })
);

// 收據
export const receiptSchema = z.object({
  merchantName: z.string().describe("Name of the store or business"),
  merchantAddress: z.string().describe("Complete address of the merchant"),
  merchantContact: z.string().optional().describe("Phone number or other contact information"),
  transactionDate: z.string().describe("Date of purchase (format: YYYY-MM-DD if possible)"),
  transactionAmount: z.string().describe("Total amount paid"),
  currency: z.string().describe("Currency used (e.g., TWD, USD)"),
  receiptSummary: z.string().describe("Brief summary of what was purchased"),
  items: z.array(
    z.object({
      name: z.string().describe("Name or description of the item"),
      quantity: z.number().describe("Quantity purchased"),
      unitPrice: z.number().describe("Price per unit"),
      totalPrice: z.number().describe("Total price for this item (quantity × unitPrice)")
    })
  ).describe("List of items purchased")
});

// 總幹事每月呈報收支列表
const itemSchema = z.object({
  serial_no: z.string().describe("序號"),
  account_item: z.string().describe("會計科目"),
  expense_summary: z.string().describe("支出摘要"),
  month_year: z.string().regex(/^\d{2,3}\/\d{2}$/, "月份應為 YY/MM 或 YYY/MM 格式 (民國年)").describe("月份 (民國年/月)"),
  amount: z.number().describe("金額"),
  payment_method: z.enum(["匯款", "領現", "轉帳", "自動扣款"]).describe("付款方式")
});

const totalSchema = z.object({
  label: z.string().describe("總計標籤"),
  amount: z.number().describe("總計金額")
});

const approverSchema = z.object({
  role: z.string().describe("簽核人角色"),
  name: z.string().nullable().describe("簽核人姓名 (若有)"),
  stamp_present: z.boolean().describe("是否有蓋章")
});

export const expenseReportSchema = z.object({
  title: z.string().describe("文件標題"),
  document_period_minguo: z.string().regex(/^\d{2,3}年\d{2}月$/, "文件期間應為 YYY年MM月 或 YY年MM月 格式 (民國年)").describe("文件期間 (民國年/月)"),
  table_headers: z.array(z.string()).describe("表格欄位標頭"),
  items: z.array(itemSchema).describe("支出項目列表"),
  total: totalSchema.describe("總計"),
  approvers: z.array(approverSchema).describe("簽核人列表")
});

export type ExpenseReport = z.infer<typeof expenseReportSchema>;